import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, inject } from '@angular/core';
import { AuthFacade, UserService } from '@dwh/dx-lib/src/lib/services/auth';
import { Observable, map } from 'rxjs';
import { PostApiService } from './shared/services/post-api.service';
import { BannerService } from './shared/services/banner.service';
import { NavigationEnd, Router } from '@angular/router';
import { GetApiService } from './shared/services/get-api.service';
import { SwUpdate } from '@angular/service-worker';
import { SignalRService } from './shared/services/signalR.service';
import { EdsNotificationService } from '@gfk/ng-lib';
import { NotificationVariant } from '@gfk/stencil-core';

export interface AutocompleteOption {
	value: string;
	label: string;
}

@Component({
	selector: 'dx-app-root',
	templateUrl: './app.component.html',
	styleUrls: ['./app.component.scss'],
})
export class AppComponent implements OnInit, OnDestroy{
	private readonly authFacade = inject(AuthFacade);
	private readonly userService = inject(UserService);
	private readonly bannerService = inject(BannerService);
	private readonly notificationService: EdsNotificationService = inject(EdsNotificationService);

	productGroup$!: Observable<AutocompleteOption[]>;
	userName$ = this.authFacade.user$.pipe(
		map((user) => {
			if (user) {
				const username = `${user.userName}`;
				localStorage.setItem('username', user.userName);
				this.getLoggedInUserRole();
				// Start SignalR connection when the component is initialized
				this.signalRService.startConnection(username);
				this.signalRService.addMessageListener();
				if(!localStorage.getItem('productGroupData')){
					this.getProductGroupList();
				}
				return username;
			}
			return '';
		})
	);
	isBannerVisible!: boolean;
	displayUI = false;
	showContent!: boolean;

	constructor(
		private router: Router,
	    private getApiService: GetApiService,
		private postApiService: PostApiService,
    	private swUpdate: SwUpdate,
		private signalRService: SignalRService
	){}


	ngOnInit(): void {
		this.router.events.subscribe((e) => {
			if (e instanceof NavigationEnd) {
			  if(!e.url.includes('redirect-web')){
				this.showContent = true;
			  }
			}
		});
		this.userService.signInUser();
		this.authFacade.registerAuthentication();
		this.userName$.subscribe();
		setTimeout(() => {
			this.displayUI = true;
		}, 1000);
		this.bannerService.isVisible$.subscribe((response: any) => {
			this.isBannerVisible = response;
		});
    	if (this.swUpdate.isEnabled) {
			this.swUpdate.available.subscribe(() => {
				console.log('New version detected. Reloading...');
				window.location.reload();
			});
		}

		// Subscribe to the message observable to update UI when new messages are received
		this.signalRService.message$.subscribe((notificationMessage) => {
			if (notificationMessage) {
				const extractedIds = this.extractIds(notificationMessage);
				if (extractedIds) {
					const { jiraId, rsId } = extractedIds;
					if (jiraId && rsId) {
						const title = 'Retailer Separation Completed';
						const message = `The request for Jira ID: ${jiraId} has been successfully processed. The CSV is now available for download.`;
						this.notifyWidget(title, 'success', message, rsId);
					}
				}
			}
		});	
	}

	ngOnDestroy(): void {
		// Stop the SignalR connection when the component is destroyed
		this.signalRService.stopConnection();
	}

	extractIds(input: string): { jiraId: string, rsId: string } | null {
		const regex = /Jira Id: (\S+).*RS Id: (\d+)/;
		const matches = input.match(regex);
		if (matches) {
		  return {
			jiraId: matches[1],
			rsId: matches[2]
		  };
		}
		return null;  // Return null if no match is found
	}

	getLoggedInUserRole(){
		this.getApiService.getLoggedInUserRole()
		.subscribe({
			next: (result: any) => {
				const approvedUserRole = result.find((item: any) => item.status == 'Approved');
				if(approvedUserRole){
					localStorage.setItem('roleData', JSON.stringify(approvedUserRole));
					this.getApiService.setUserRoleSubject(approvedUserRole);
				}
				else{
					this.getApiService.setUserRoleSubject('');
					localStorage.removeItem('roleData');
				}	
			},
			error: (error) => {
				if(error.status == 404){
					localStorage.removeItem('roleData');
				}
				else if(error.status == 403){
					UserService.forbiddenError = true;
				}
				this.getApiService.setUserRoleSubject('');
			}
		})
	}

	getProductGroupList(){
		const countryIds: any = [12, 13, 15, 16, 17, 18, 19, 23, 25, 29, 30, 37, 45, 47, 73, 85, 86, 87];
		this.productGroup$ = this.postApiService.getAsyncProductGroup([1,2,3], countryIds, [], [], [])
		.pipe(
			map((productGroups: any) => productGroups.map((productGroup: any) => (
				{
					description: productGroup.description + ' ('+productGroup.id+')',
					id: productGroup.id,
				}
			)))
		);
		this.productGroup$.subscribe({
			next: (result) => {
				const productGroupData = {
					countryIds: countryIds,
					productGroupList: result
				}
				if(!localStorage.getItem('productGroupData')){
					localStorage.setItem('productGroupData', JSON.stringify(productGroupData));
				}
			}
		});
	}

	notifyWidget(title: string, notificationType: string, message?: string, rsId?: any): void {
		if(notificationType == 'info'){
			this.notificationService.showNotification(title, {
				variant: NotificationVariant.INFO,
				message: message || ''
			});
		}
		else if(notificationType == 'error'){
			this.notificationService.showNotification(title, {
				message: message || '',
				duration: 15000,
				variant: NotificationVariant.ERROR,
			});
		}
		else{
			this.notificationService.showNotification(title, {
				message: message || '',
				duration : 50000,
				variant: NotificationVariant.SUCCESS,
				action: () => this.router.navigate([`/retailer-separation/list/${rsId}`]),
				actionLabel: 'Click here to navigate to the link.'
			});
		}
	}

}