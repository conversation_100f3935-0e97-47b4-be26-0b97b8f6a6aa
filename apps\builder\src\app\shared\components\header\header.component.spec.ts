import { ComponentFixture, TestBed } from '@angular/core/testing';
import { HeaderComponent } from './header.component';
import { ConfigService } from '@builder/shared/services/config.service';
import { Router, NavigationEnd } from '@angular/router';
import { of, Subject } from 'rxjs';
import { AuthFacade } from '@dwh/dx-lib/src/lib/services/auth';
import { By } from '@angular/platform-browser';

interface SignOutResponse {
  links: any;
  // Define other properties if needed
}

describe('HeaderComponent', () => {
  let component: HeaderComponent;
  let fixture: ComponentFixture<HeaderComponent>;
  let configService: ConfigService;
  let router: Router;
  let authFacade: AuthFacade;

  beforeEach(async () => {
    const configServiceMock = {
      getVersion: jest.fn().mockReturnValue('v1.0.0'),
      getConfigVariable: jest.fn().mockReturnValue(of({
        UNDER_MAINTAINANCE: false  // Mock response for getConfigVariable
      }))
    };

    const routerMock = {
      url: '/home',
      events: new Subject<any>(),
      navigate: jest.fn(),
    };

    const authFacadeMock = {
      user$: of({ userName: 'testUser' }),
      signOut: jest.fn().mockReturnValue(of<SignOutResponse>({ links: {} })),
      signIn: jest.fn(),
    };

    await TestBed.configureTestingModule({
      declarations: [HeaderComponent],
      providers: [
        { provide: ConfigService, useValue: configServiceMock },
        { provide: Router, useValue: routerMock },
        { provide: AuthFacade, useValue: authFacadeMock },
      ],
    }).compileComponents();

    configService = TestBed.inject(ConfigService);
    router = TestBed.inject(Router);
    authFacade = TestBed.inject(AuthFacade);
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(HeaderComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize the version on init', () => {
    expect(component.version).toBe('v1.0.0');
  });

  it('should set hideNav to true when on /home route', () => {
    component.ngOnInit();
    expect(component.hideNav).toBe(true);
  });

  it('should set hideNav to false when not on /home route', () => {
    (router.url as string) = '/another-route';
    component.ngOnInit();
    expect(component.hideNav).toBe(false);
  });

  it('should initialize userName from localStorage', () => {
    localStorage.setItem('username', 'testUser');
    component.ngOnInit();
    expect(component.userName).toBe('testUser');
  });

  it('should update userDetails and localStorage on userName$ subscription', () => {
    component.ngOnInit();
    expect(localStorage.getItem('username')).toBe('testUser');
  });

  it('should update currentTab on NavigationStart event', () => {
    const navigationStart = new NavigationEnd(1, '/new-route', '/new-route');
    (router.events as Subject<any>).next(navigationStart);
    expect(component.currentTab);
  });

  it('should clear localStorage and call signOut on signOut', () => {
    const spy = jest.spyOn(authFacade, 'signOut').mockReturnValue(of<SignOutResponse>({ links: {} }));
    component.signOut();
    expect(localStorage.clear);
    expect(spy).toHaveBeenCalled();
  });

  it('should call signIn on signIn', () => {
    const spy = jest.spyOn(authFacade, 'signIn');
    component.signIn();
    expect(spy).toHaveBeenCalled();
  });

  it('should open support link on navigateToSupportLink', () => {
    const spy = jest.spyOn(window, 'open').mockImplementation(() => null);
    component.navigateToSupportLink();
    expect(spy).toHaveBeenCalledWith('https://nielsenenterprise.sharepoint.com/sites/MIOPS/SitePages/Builder-X.aspx', '_blank', 'noopener');
  });

  it('should call getConfigVariable on init and set underMaintainance correctly', () => {
    // Mocked value from getConfigVariable
    component.ngOnInit();
    expect(component.underMaintainance).toBe(false);  // Mocked response value
  });
});
