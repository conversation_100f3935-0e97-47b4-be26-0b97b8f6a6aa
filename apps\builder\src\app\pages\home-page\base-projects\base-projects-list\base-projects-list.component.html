<div class="relative-position bp-listing">
	<div class="heading p-1.5">
		<div class="header-spacing mx-6">
			<div>
				<h4 class="mb-0 font-bold">Open a base project</h4>
			</div>
			<div class="text-right">
				<button
					*ngIf="userRole?.name === 'Master'"
					gfk-button
					type="primary"
					class="jira-button-margin-right btn-secondary gfk-btn"
					routerLink="/base-projects/create"
					eds-id="add-base-project-button"
					testId="add-base-project-button">
					Create new Base Project
				</button>
			</div>
		</div>
		<eds-tray [visible]="showTray" heading="Filters" [size]="'min'" [edsId]="'tray'" (edsClose)="onCloseFilterTray()" (edsOpen)="onOpenFilterTray()">
			<div slot="content" class="content-spacing">
				<dx-filter-tray-form
					[sliceName]="'baseProject'"
					[resetForm]="resetForm"
					[panelList]="panelList"
					[countriesList]="countriesList"
					[periodicityData]="periodicityData"
					[projectSubTypeList]="baseProjectTypeList"
					[baseProjectUserList]="baseProjectUserList"
					(selectedFilters)="getSelectedFilters($event)">
				</dx-filter-tray-form>
			</div>
			<div class="action-bar" slot="actions">
				<button gfk-button eds-id="reset-filters-button" testId="reset-filters-button" variant="secondary" (click)="resetFilterTray()">Reset Filters</button>
				<button gfk-button eds-id="apply-button" testId="apply-button" variant="primary" (click)="confirmApply(1)">Apply</button>
			</div>
		</eds-tray>
	</div>

	<div class="filter-area">
		<div class="toggle-button">
			<div>
				<div class="input-container">
					<input
						gfkInput
						[value]="inputValue"
						(input)="onInputChange($event.target.value)"
						placeholder="Search by BP ID, BP Name or QCID"
						eds-id="input-search-project"
					/>
					<gfk-icon *ngIf="inputValue.length > 0" [icon]="'close'" class="color-gfk-organge" (click)="closeClick()"></gfk-icon>

					<button
						gfk-button
						type="primary"
						eds-id="search-base-project-button"
						testId="search-base-project-button"
						class="btn-secondary gfk-btn inline-search-button"
						(click)="confirmApply(1)"
						[disabled]="loadBaseProjectData">
						Search
					</button>
				</div>
			</div>

			<div [ngClass]="FilterTrayOpenFlag ? 'filter-tray-open' : 'filter-tray-close'">
				<dx-filter-button showFilterCount="{{ filterCount }}" (isShownFilterTray)="isShownFilterTrayEventHandler($event)" [disableFilterBtn]="loadBaseProjectData"></dx-filter-button>
			</div>
		</div>
		<div>
			<eds-chip label="{{ chip.label }}" class="chips mr-2" *ngFor="let chip of chips"><span *ngIf="chip.value">: {{ chip.value }}</span></eds-chip>
		</div>
	</div>

	<div class="mx-6">
		<article class="table-grid mt-5">
			<eds-table eds-id="base-project-table" hasRowSelect="checkbox" [isLoading]="loadBaseProjectData" (edsRowSelected)="onSelect($event, baseProjectData); rowsSelectedHandler($event, baseProjectData)">
				<thead slot="thead">
					<eds-tr variant="header">
						<eds-th variant="selector" [isSelected]="areAllSelectedWithPagination(visibleBaseProjects)" [columnIndex]="0" *ngIf="userRole?.name === 'Master' || userRole?.name === 'Account Manager'">
							Base Project ID
						</eds-th>
						<eds-th variant="" [isSelected]="areAllSelectedWithPagination(visibleBaseProjects)" [columnIndex]="0" *ngIf="!userRole || userRole?.name === 'MarketData QC'">
							<span class="pl-9">Base Project ID</span>
						</eds-th>
						<eds-th column-index="1">QC Project ID</eds-th>
						<eds-th column-index="2">Base Project Name</eds-th>
						<eds-th column-index="3">Panel</eds-th>
						<eds-th column-index="3">Data Type</eds-th>
						<eds-th column-index="3">Purpose</eds-th>
						<eds-th column-index="4">Type</eds-th>
						<eds-th column-index="5">Country</eds-th>
						<eds-th column-index="6">PGs</eds-th>
						<eds-th column-index="7">Periodicity</eds-th>
						<eds-th column-index="6">Last Updated By</eds-th>
						<eds-th column-index="7">Last Updated When</eds-th>
					</eds-tr>
				</thead>

				<tbody slot="tbody">
					<ng-container *ngIf="visibleBaseProjects?.length > 0; else noData">
						<eds-tr *ngFor="let baseProject of visibleBaseProjects; trackBy: trackById" [rowIndex]="baseProject.id">
							<eds-td [isSelected]="isSelected(baseProject.id)" variant="selector" *ngIf="userRole?.name === 'Master' || userRole?.name === 'Account Manager'">
								<span class="bp-id-style cursor-pointer" [routerLink]="'/base-projects/update/'+baseProject.id">{{ baseProject.id }}</span>
							</eds-td>
							<eds-td *ngIf="!userRole || userRole?.name === 'MarketData QC'">
								<span class="bp-id-style cursor-pointer pl-9" [routerLink]="'/base-projects/update/'+baseProject.id">{{ baseProject.id }}</span>
							</eds-td>
							<eds-td>{{ baseProject.qcprojectId ? baseProject.qcprojectId : '' }}</eds-td>
							<eds-td>
								<span *ngIf="baseProject.name.length <= 31">{{ baseProject.name }}</span>
								<span [gfk-tooltip]="baseProject?.name" *ngIf="baseProject.name.length > 31">{{ baseProject.name }}</span>
							</eds-td>
							<eds-td>{{ baseProject.panelName }}</eds-td>
							<eds-td>
								<span [gfk-tooltip]="baseProject?.dataType?.description" *ngIf="baseProject?.dataType?.description">{{ baseProject?.dataType?.name }}</span>
								<span *ngIf="!baseProject?.dataType?.description">{{ baseProject?.dataType?.name }}</span>
							</eds-td>
							<eds-td>{{ baseProject?.purpose?.name }}</eds-td>
							<eds-td>{{ baseProject.typeName }}</eds-td>
							<eds-td>{{ baseProject.countryName ? baseProject.countryName : 'N/A' }}</eds-td>
							<eds-td>
								<gfk-badge variant="soft-action">
									{{ baseProject.productGroupCount }}
								</gfk-badge>
							</eds-td>
							<eds-td>{{baseProject.periodicityName }}</eds-td>
							<eds-td>{{baseProject.updatedBy }}</eds-td>
							<eds-td>{{baseProject.updatedWhenCET }}</eds-td>
						</eds-tr>
					</ng-container>
					<ng-template #noData>
						<eds-tr><eds-td colspan="6">{{filterApplied ? 'No records match the applied filter(s)' : 'Search or Open the Filter to see Base and QC Projects.'}}</eds-td></eds-tr>
					</ng-template>
				</tbody>
			</eds-table>

			<gfk-pagination
				*ngIf="baseProjectData?.length > 0 && baseProjectData?.length > defaultPageSize"
				id="pagination-create-ld"
				[itemsPerPageOptions]="pageSizeOptions"
				[totalCount]="baseProjectData?.length"
				[position]="'right'"
				[showItemsPerPage]="true"
				[showFirstAndLast]="true"
				[defaultPageSize]="defaultPageSize"
				[currentPage]="currentPage"
				(onPage)="onPageChangeBaseProjects($event)">
			</gfk-pagination>
		</article>
	</div>
	<div *ngIf="hasSelection">
		<div class="h-32"></div>
		<div class="selected-base-project-footer">
			<div>
				{{ selectedCount + ' Base Projects Selected' }}
			</div>
			<div class="flex">
				<button
					gfk-button
					type="primary"
				    class="jira-button-margin-right transparent-background-Restore-btn"
            		(click)="openRestoreConfirmationModal()"
					eds-id = "restore-deleted-bps-button"
					testId = "restore-deleted-button"
					*ngIf="userRole?.name === 'Master' && allDeletedBPSelected ">
			<eds-icon
                size="sm"
                icon="refresh"
                class="inline-flex fill-white stroke-white h-5 w-5 hydrated"
                color="brand">
			</eds-icon>
				Restore Base Projects				
				</button>
				<button
					gfk-button
					type="primary"
					class="jira-button-margin-right btn-secondary gfk-btn"
					(click)="copySelectedBaseProjectIds()"
					eds-id="copy-bp-ids-button"
					testId="copy-bp-ids-button">
					<eds-icon
					size="sm"
                    icon="copy"
                    class="inline-flex fill-white stroke-white h-5 w-5 hydrated"
                    color="white">
					</eds-icon>
					Copy BP Id's
				</button>
				<button
					*ngIf="userRole && userRole?.name !== 'MarketData QC' && !allDeletedBPSelected"
					gfk-button
					type="primary"
					class="jira-button-margin-right btn-secondary gfk-btn"
					(click)="openAddBPSecurityUsersModal()"
					eds-id="add-qc-security-users-button"
					testId="add-qc-security-users-button">
					<eds-icon
						size="sm"
						icon="person_add"
						class="inline-flex fill-white stroke-white h-5 w-5 hydrated"
						color="white">
					</eds-icon>
					Add BP Security Users
				</button>
				<button
					*ngIf="userRole && userRole?.name !== 'MarketData QC' && !allDeletedBPSelected"
					gfk-button
					type="primary"
					class="jira-button-margin-right btn-secondary gfk-btn"
					(click)="openAddQCSecurityUsersModal()"
					[disabled]="disableAddUserBtn"
					eds-id="add-qc-security-users-button"
					testId="add-qc-security-users-button">
					<eds-icon
						size="sm"
						icon="person_add"
						class="inline-flex fill-white stroke-white h-5 w-5 hydrated"
						color="white">
					</eds-icon>
					Add QC Security Users
				</button>
				<button
					*ngIf="userRole?.name === 'Master' && !allDeletedBPSelected"
					gfk-button
					type="primary"
					class="btn-secondary gfk-btn transparent-background-delete-btn"
					(click)="openDeleteConfirmationModal()"
					eds-id="delete-base-project-button"
					testId="delete-base-project-button">
					<eds-icon
						size="sm"
						icon="warning"
						class="inline-flex fill-brand stroke-brand h-5 w-5 hydrated"
						color="brand">
					</eds-icon>
					Delete Base Project(s)
				</button>
			</div>
		</div>
	</div>
</div>
<!-- The Modal -->
<gfk-modal
	[triggerModal]="deleteConfirmationModal"
	modalTitle="Confirm deletion of Base Project(s)"
	cancelTitle="Cancel"
	confirmTitle="Yes, delete"
	(onAction)="deleteSelectedBaseProject($event)">
	<p>
		The selected base projects will be deleted. Links to predecessor projects or production projects will be removed. Would you like to
		proceed?
	</p>
</gfk-modal>
<!-- Restore Confirmation Modal -->
<gfk-modal
	[triggerModal]="restoreConfirmationModal"
	modalTitle="Confirm restoration of Base Project(s)"
	cancelTitle="Cancel"
	confirmTitle="Yes, restore"
	(onAction)="onRestoreBaseProject()">
	<p>
		The selected base projects will be restored. Would you like to proceed?
	</p>
</gfk-modal>
<!-- Delete Validation Modal -->
<gfk-modal
	[triggerModal]="deleteValidationModal"
	modalTitle="Delete Base Projects Results"
	cancelTitle=""
	confirmTitle="Okay"
	(onAction)="closeDeleteValidationModal()">
	<div>
		<p *ngIf="displayDeleteWithErrorMessage">
			Following BaseProject(s) deleted successfully: <br>
			<span class="text-blue-color">{{deletedBps.toString().replaceAll(',', ', ')}}</span>
		</p>
		<p>
			Following BaseProject(s) cannot be deleted due to dependencies or linkages to other projects:
		</p>
		<div class="bp-conflict-detail-height">
			<div *ngFor="let bpConflict of bpDeleteConflictDetails" class="bp-conflict-detail-card">
				<p class="flex mb-0">
					<span class="mr-2">
						<eds-icon
							eds-id="dx-qc-settings-info"
							icon="info"
							color="inactive"
							size="sm" >
						</eds-icon>
					</span>
					<span class="text-lighter">
						BP ID: {{bpConflict?.baseProjectId}}
					</span>
				</p>
				<p *ngIf="bpConflict?.dependency?.productionProjectIds?.length">
					<span class="text-brand">ProductionProject: </span>
					<span>{{(bpConflict?.dependency?.productionProjectIds?.length) ? (bpConflict?.dependency?.productionProjectIds?.toString().replaceAll(',', ', ')) : '-'}}</span>
				</p>
				<p *ngIf="bpConflict?.dependency?.reportingProjectIds?.length">
					<span class="text-brand">ReportingProject: </span>
					<span>{{(bpConflict?.dependency?.reportingProjectIds?.length) ? (bpConflict?.dependency?.reportingProjectIds?.toString().replaceAll(',', ', ')) : '-'}}</span>
				</p>
				<p class="m-0" *ngIf="bpConflict?.dependency?.rbBaseProjectIds?.length">
					<span class="text-brand">RB Base Project: </span>
					<span>{{(bpConflict?.dependency?.rbBaseProjectIds?.length) ? (bpConflict?.dependency?.rbBaseProjectIds?.toString().replaceAll(',', ', ')) : '-'}}</span>
				</p>
				<p class="m-0" *ngIf="(bpConflict?.dependency !== null) && bpConflict?.dependency?.qcStatusPeriodId!==0">
					<span class="text-brand">* A QC Period is detected with status set to QC which prevents BP deletion: </span>
					<span>{{bpConflict?.dependency?.qcStatusPeriodId}}</span>
				</p>
				<p class="m-0" *ngIf="(bpConflict?.dependency === null) && bpConflict?.statusMsg?.includes('BaseProject Can\'t be deleted because it is part of a pending Retailer Separation Request')">
					<span class="text-brand">* BaseProject cannot be deleted due to a pending Retailer Separation Request. </span>
				</p>
			</div>
		</div>
	</div>
</gfk-modal>
<!-- Add QC Security Users Modal -->
<gfk-modal
	[triggerModal]="addQCSecurityUsersModal"
	[modalTitle]="'Add QC Security Users'"
	cancelTitle="Cancel"
	[confirmTitle]="'Save'"
	[confirmDisabled]="addQCSecurityUsersForm?.invalid"
	(onAction)="addQCSecurityUsers($event);">
	<form [formGroup]="addQCSecurityUsersForm" *ngIf="addQCSecurityUsersModal">
		<dx-chip-autocomplete
			class="filters-align"
			(val)="userkey($event)"
			formControlName="users"
			[options]="userList"
			[id]="'user-input'"
			[placeholder]="'Users'"
			[label]="'Users'">
		</dx-chip-autocomplete>
	</form>
</gfk-modal>

<!-- Add BP Security Users Modal -->
<gfk-modal
[triggerModal]="addBPSecurityUsersModal"
[modalTitle]="'Add BP Security Users'"
cancelTitle="Cancel"
[confirmTitle]="'Save'"
[confirmDisabled]="addBPSecurityUsersForm?.invalid"
(onAction)="addBPSecurityUsers($event);">
<form [formGroup]="addBPSecurityUsersForm" *ngIf="addBPSecurityUsersModal">
  <dx-chip-autocomplete
    class="filters-align"
    (val)="userkey($event)"
    formControlName="users"
    [options]="userList"
    [id]="'user-input'"
    [placeholder]="'Users'"
    [label]="'Users'">
  </dx-chip-autocomplete>
</form>
</gfk-modal>

