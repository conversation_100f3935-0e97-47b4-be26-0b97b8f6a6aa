
import { AfterViewInit, Component, HostListener, Inject, On<PERSON><PERSON><PERSON>, OnInit, Renderer2, RendererFactory2, inject } from '@angular/core';
import { ConfigService } from '@builder/shared/services/config.service';
import { Observable, Subject, map } from 'rxjs';
import { Router, NavigationStart, NavigationEnd } from '@angular/router';
import { AuthFacade } from '@dwh/dx-lib/src/lib/services/auth';
import { DOCUMENT } from '@angular/common';

@Component({
	selector: 'dx-header',
	templateUrl: './header.component.html',
	styleUrls: ['./header.component.scss'],
})
export class HeaderComponent implements OnInit, OnDestroy, AfterViewInit {

	private readonly authFacade = inject(AuthFacade);
	
	LandingPageURL = "/https://home.de.vpc1445.in.gfk.com";
	TestApp = 'Builder X';
	withProfile = true;
	withSection = false;
	version?: string;
	currentTab = window.location.href.split('/')[3];
	loginDisplay = false;
	userName!: string;
	userName$!: Observable<any>;
	userDetails!: any;
	private config = window.CONFIG;
	underMaintainance: boolean = this.config.get('UNDER_MAINTAINANCE') === 'true';

	private readonly _destroying$ = new Subject<void>();
	event$ 
	hideNav = true;
	private observer!: MutationObserver;
  	private renderer!: Renderer2;
  
	constructor(
		private configService: ConfigService, 
		rendererFactory: RendererFactory2,
		@Inject(DOCUMENT) private document: Document,
		private router: Router
	) {
		router.events.subscribe((val) => {
			if (val instanceof NavigationEnd) {
				if(val && val.url == '/home'){
					this.hideNav = true;
				}
				else{
					this.hideNav = false;
				}
			}
		});
		const packageVersion = this.configService.getVersion();
		this.version = packageVersion.substring(packageVersion.indexOf('_') + 1);
		this.renderer = rendererFactory.createRenderer(null, null);
	}
  
	@HostListener('window:resize', ['$event'])
	onResize() {
	  this.getMarginTop(0);
	}
  
  
	tabActive(activetab: string) {
	  if (this.currentTab == activetab) {
		this.currentTab = '';
	  } else {
		this.currentTab = activetab;
	  }
	}
  
	getMarginTop(number) {
	  // if (this.IsSubNavBarOpened)
		return number + 25 +'px';
	  // else
	  //   return (number + 9) - 56 +'px';
	}
  
	ngOnInit(): void {
		if(this.router.url == '/home'){
			this.hideNav = true;
		}
		else{
			this.hideNav = false;
		}
		this.userName$ = this.authFacade.user$.pipe(
			map((user) => {
				if (user) {
					localStorage.setItem('username', user.userName);
					localStorage.setItem('user', JSON.stringify(user));
					return user;
				}
				return '';
			})
		);
		this.userName$.subscribe({
			next: (result) => {
				result.name = result.email.split('@')[0];
				if (result.name.includes('.')) {
					result.firstName = result.name.split('.')[0].charAt(0).toUpperCase() + result.name.split('.')[0].slice(1);
					result.lastName = result.name.split('.')[1].charAt(0).toUpperCase() + result.name.split('.')[1].slice(1);
				}
				else {
					result.firstName = result.name;
					result.lastName = '';
				}
				this.userDetails = result;
			}
		});
		this.userName =localStorage.getItem('username') || '';
	  	this.tabActive(this.currentTab);
	}

	ngAfterViewInit(): void {
		this.navigateToLandingPage();
	}

	navigateToLandingPage() {
		this.observer = new MutationObserver(() => {
		  const appNameEl = this.document.querySelector('.app-name');
		  if (appNameEl && !appNameEl.hasAttribute('data-router-initialized')) {
			this.renderer.setAttribute(appNameEl, 'data-router-initialized', 'true');
			this.renderer.setStyle(appNameEl, 'cursor', 'pointer');
			this.renderer.listen(appNameEl, 'click', () => {
			  this.router.navigateByUrl('/home');
			});
		  }
		});
	
		this.observer.observe(this.document.body, {
		  childList: true,
		  subtree: true
		});
	}
  
	// setLoginDisplay() {
	//   this.loginDisplay = this.authService.instance.getAllAccounts().length > 0;
	// }
  
	checkAndSetActiveAccount() {
	  /**
	   * If no active account set but there are accounts signed in, sets first account to active account
	   * To use active account set here, subscribe to inProgress$ first in your component
	   * Note: Basic usage demonstrated. Your app may require more complicated account selection logic
	   */
	  // const activeAccount = this.authService.instance.getActiveAccount();
	//   let accounts;
  
	  // if (!activeAccount && this.authService.instance.getAllAccounts().length > 0) {
	  //   accounts = this.authService.instance.getAllAccounts();
	  //   this.authService.instance.setActiveAccount(accounts[0]);
	  // }
	  // return activeAccount || (accounts ? accounts[0] : null);
	}
  
	logout() {
	  // this.authService.logoutRedirect();
	}
  
	ngOnDestroy(): void {
	  this._destroying$.next(undefined);
	  this._destroying$.complete();
	  if (this.observer) {
		this.observer.disconnect();
	  }
	}
  
	detectRouteChange(){
	  this.router.events.subscribe((e) => {
		if (e instanceof NavigationStart) {
		  this.currentTab = e.url;
		}
	  });
	}

	navigateToSupportLink() {
		window.open("https://nielsenenterprise.sharepoint.com/sites/MIOPS/SitePages/Builder-X.aspx", '_blank', 'noopener');
	}

	signOut(): void {
		localStorage.clear();
		this.authFacade.signOut().subscribe();
	}

	signIn(): void {
		this.authFacade.signIn();
	}
}
